#!/usr/bin/env python3
"""
Electric Demand Forecasting with ARIMA/SARIMA and Holt-Winter Methods
3-Phase Methodology Implementation
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller, kpss
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from sklearn.metrics import mean_absolute_percentage_error
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class ElectricDemandForecaster:
    def __init__(self, data_path):
        """Initialize the forecaster with data loading and preparation."""
        self.data_path = data_path
        self.df = None
        self.train_data = None
        self.val_data = None
        self.test_data = None
        self.sarimax_model = None
        self.holt_winter_model = None
        
    def load_and_prepare_data(self):
        """Phase 1: Data preparation with datetime indexing and exogenous variable encoding."""
        print("=== PHASE 1: DATA PREPARATION ===")
        
        # Load data
        self.df = pd.read_csv(self.data_path)
        print(f"Data loaded: {self.df.shape[0]} rows, {self.df.shape[1]} columns")
        
        # Convert DateTime to datetime index
        self.df['DateTime'] = pd.to_datetime(self.df['DateTime'])
        self.df.set_index('DateTime', inplace=True)

        # For faster processing, use daily aggregation
        print("Aggregating to daily frequency for faster processing...")
        daily_df = self.df.resample('D').agg({
            'Demand': 'mean',
            'Temperature': 'mean',
            'Holiday': 'max',  # If any hour in day is holiday, day is holiday
            'Year': 'first',
            'Month': 'first',
            'Day': 'first',
            'Hour': 'mean',  # Average hour of day
            'WeekDay': 'first'
        })
        self.df = daily_df
        
        # Encode exogenous variables
        # Holiday encoding (weekends already count as holidays based on data)
        self.df['IsHoliday'] = self.df['Holiday'].astype(int)
        
        # Create cyclical features for time components (adjusted for daily data)
        self.df['DayOfYear'] = self.df.index.dayofyear
        self.df['DayOfYear_sin'] = np.sin(2 * np.pi * self.df['DayOfYear'] / 365.25)
        self.df['DayOfYear_cos'] = np.cos(2 * np.pi * self.df['DayOfYear'] / 365.25)
        self.df['WeekDay_sin'] = np.sin(2 * np.pi * self.df['WeekDay'] / 7)
        self.df['WeekDay_cos'] = np.cos(2 * np.pi * self.df['WeekDay'] / 7)
        
        print("Exogenous variables encoded successfully")
        print(f"Date range: {self.df.index.min()} to {self.df.index.max()}")
        
    def exploratory_data_analysis(self):
        """Perform EDA with decomposition and stationarity testing."""
        print("\n=== EXPLORATORY DATA ANALYSIS ===")
        
        # Basic statistics
        print("\nDemand Statistics:")
        print(self.df['Demand'].describe())
        
        # Time series plot
        plt.figure(figsize=(15, 8))
        plt.subplot(2, 1, 1)
        plt.plot(self.df.index, self.df['Demand'], alpha=0.7)
        plt.title('Electric Demand Time Series')
        plt.ylabel('Demand')
        plt.grid(True)
        
        # Monthly aggregation for trend visualization
        monthly_demand = self.df['Demand'].resample('M').mean()
        plt.subplot(2, 1, 2)
        plt.plot(monthly_demand.index, monthly_demand.values, marker='o')
        plt.title('Monthly Average Demand')
        plt.ylabel('Average Demand')
        plt.grid(True)
        plt.tight_layout()
        plt.show()
        
        # Seasonal decomposition
        print("\nPerforming seasonal decomposition...")
        # Use weekly data for decomposition to manage computational load
        weekly_data = self.df['Demand'].resample('W').mean()
        decomposition = seasonal_decompose(weekly_data, model='additive', period=52)
        
        fig, axes = plt.subplots(4, 1, figsize=(15, 12))
        decomposition.observed.plot(ax=axes[0], title='Original')
        decomposition.trend.plot(ax=axes[1], title='Trend')
        decomposition.seasonal.plot(ax=axes[2], title='Seasonal')
        decomposition.resid.plot(ax=axes[3], title='Residual')
        plt.tight_layout()
        plt.show()
        
        # Stationarity tests
        print("\n=== STATIONARITY TESTS ===")
        self.stationarity_tests(self.df['Demand'])
        
    def stationarity_tests(self, series):
        """Perform ADF and KPSS stationarity tests."""
        # ADF Test
        adf_result = adfuller(series.dropna())
        print(f"\nAugmented Dickey-Fuller Test:")
        print(f"ADF Statistic: {adf_result[0]:.6f}")
        print(f"p-value: {adf_result[1]:.6f}")
        print(f"Critical Values: {adf_result[4]}")
        
        if adf_result[1] <= 0.05:
            print("Result: Series is stationary (reject null hypothesis)")
        else:
            print("Result: Series is non-stationary (fail to reject null hypothesis)")
        
        # KPSS Test
        kpss_result = kpss(series.dropna())
        print(f"\nKPSS Test:")
        print(f"KPSS Statistic: {kpss_result[0]:.6f}")
        print(f"p-value: {kpss_result[1]:.6f}")
        print(f"Critical Values: {kpss_result[3]}")
        
        if kpss_result[1] <= 0.05:
            print("Result: Series is non-stationary (reject null hypothesis)")
        else:
            print("Result: Series is stationary (fail to reject null hypothesis)")
    
    def create_train_val_test_split(self):
        """Create 60/20/20 chronological split."""
        print("\n=== DATA SPLITTING (60/20/20) ===")
        
        n = len(self.df)
        train_size = int(0.6 * n)
        val_size = int(0.2 * n)
        
        self.train_data = self.df.iloc[:train_size].copy()
        self.val_data = self.df.iloc[train_size:train_size + val_size].copy()
        self.test_data = self.df.iloc[train_size + val_size:].copy()
        
        print(f"Training set: {len(self.train_data)} samples ({self.train_data.index[0]} to {self.train_data.index[-1]})")
        print(f"Validation set: {len(self.val_data)} samples ({self.val_data.index[0]} to {self.val_data.index[-1]})")
        print(f"Test set: {len(self.test_data)} samples ({self.test_data.index[0]} to {self.test_data.index[-1]})")
        
    def analyze_acf_pacf(self, data, title=""):
        """Analyze ACF and PACF for parameter selection."""
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        plot_acf(data.dropna(), ax=axes[0], lags=50, title=f'ACF {title}')
        plot_pacf(data.dropna(), ax=axes[1], lags=50, title=f'PACF {title}')
        plt.tight_layout()
        plt.show()
        
    def phase2_initial_training(self):
        """Phase 2: Initial training on 60% with model diagnostics and validation."""
        print("\n=== PHASE 2: INITIAL TRAINING AND VALIDATION ===")
        
        # ACF/PACF analysis for parameter selection
        print("\nAnalyzing ACF/PACF for parameter selection...")
        self.analyze_acf_pacf(self.train_data['Demand'], "- Training Data")
        
        # Manual SARIMAX parameter selection based on ACF/PACF
        # Based on typical electric demand patterns: daily (24h) and weekly (168h) seasonality
        print("\nTraining SARIMAX model...")
        
        # Prepare exogenous variables (adjusted for daily data)
        exog_vars = ['Temperature', 'IsHoliday', 'WeekDay_sin', 'WeekDay_cos', 'DayOfYear_sin', 'DayOfYear_cos']
        train_exog = self.train_data[exog_vars]
        val_exog = self.val_data[exog_vars]
        
        # SARIMAX model with simplified parameters for faster training
        self.sarimax_model = SARIMAX(
            self.train_data['Demand'],
            exog=train_exog,
            order=(1, 1, 1),  # Simplified ARIMA parameters
            seasonal_order=(1, 0, 1, 7),  # Weekly seasonality for daily data
            enforce_stationarity=False,
            enforce_invertibility=False
        )
        
        sarimax_fitted = self.sarimax_model.fit(disp=False)
        print("SARIMAX model fitted successfully")
        
        # Holt-Winter model
        print("\nTraining Holt-Winter model...")
        self.holt_winter_model = ExponentialSmoothing(
            self.train_data['Demand'],
            trend='add',
            seasonal='mul',
            seasonal_periods=7  # Weekly seasonality for daily data
        )
        
        hw_fitted = self.holt_winter_model.fit(optimized=True)
        print("Holt-Winter model fitted successfully")
        
        # Validation predictions
        print("\nGenerating validation predictions...")
        
        # SARIMAX validation predictions
        sarimax_val_pred = sarimax_fitted.forecast(steps=len(self.val_data), exog=val_exog)
        
        # Holt-Winter validation predictions
        hw_val_pred = hw_fitted.forecast(steps=len(self.val_data))
        
        # Calculate MAPE for validation
        sarimax_val_mape = mean_absolute_percentage_error(self.val_data['Demand'], sarimax_val_pred) * 100
        hw_val_mape = mean_absolute_percentage_error(self.val_data['Demand'], hw_val_pred) * 100
        
        print(f"\nValidation Results:")
        print(f"SARIMAX MAPE: {sarimax_val_mape:.2f}%")
        print(f"Holt-Winter MAPE: {hw_val_mape:.2f}%")
        
        # Validation visualization
        plt.figure(figsize=(15, 8))
        plt.plot(self.val_data.index, self.val_data['Demand'], label='Actual', alpha=0.8)
        plt.plot(self.val_data.index, sarimax_val_pred, label='SARIMAX', alpha=0.8)
        plt.plot(self.val_data.index, hw_val_pred, label='Holt-Winter', alpha=0.8)
        plt.title('Validation Set Predictions')
        plt.xlabel('Date')
        plt.ylabel('Demand')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.show()
        
        return sarimax_fitted, hw_fitted

    def phase3_final_training_and_testing(self):
        """Phase 3: Retraining on 80% and final validation on 20%."""
        print("\n=== PHASE 3: FINAL TRAINING AND TESTING ===")

        # Combine training and validation data (80%)
        train_val_data = pd.concat([self.train_data, self.val_data])

        print(f"Combined training data: {len(train_val_data)} samples")
        print(f"Test data: {len(self.test_data)} samples")

        # Prepare exogenous variables (adjusted for daily data)
        exog_vars = ['Temperature', 'IsHoliday', 'WeekDay_sin', 'WeekDay_cos', 'DayOfYear_sin', 'DayOfYear_cos']
        train_val_exog = train_val_data[exog_vars]
        test_exog = self.test_data[exog_vars]

        # Retrain SARIMAX on 80% data
        print("\nRetraining SARIMAX model on 80% data...")
        sarimax_final = SARIMAX(
            train_val_data['Demand'],
            exog=train_val_exog,
            order=(1, 1, 1),
            seasonal_order=(1, 0, 1, 7),
            enforce_stationarity=False,
            enforce_invertibility=False
        )
        sarimax_final_fitted = sarimax_final.fit(disp=False)

        # Retrain Holt-Winter on 80% data
        print("Retraining Holt-Winter model on 80% data...")
        hw_final = ExponentialSmoothing(
            train_val_data['Demand'],
            trend='add',
            seasonal='mul',
            seasonal_periods=7
        )
        hw_final_fitted = hw_final.fit(optimized=True)

        # Final test predictions
        print("\nGenerating final test predictions...")

        # SARIMAX test predictions
        sarimax_test_pred = sarimax_final_fitted.forecast(steps=len(self.test_data), exog=test_exog)

        # Holt-Winter test predictions
        hw_test_pred = hw_final_fitted.forecast(steps=len(self.test_data))

        # Calculate final MAPE
        sarimax_test_mape = mean_absolute_percentage_error(self.test_data['Demand'], sarimax_test_pred) * 100
        hw_test_mape = mean_absolute_percentage_error(self.test_data['Demand'], hw_test_pred) * 100

        print(f"\nFinal Test Results:")
        print(f"SARIMAX MAPE: {sarimax_test_mape:.2f}%")
        print(f"Holt-Winter MAPE: {hw_test_mape:.2f}%")

        # Final test visualization
        plt.figure(figsize=(15, 8))
        plt.plot(self.test_data.index, self.test_data['Demand'], label='Actual', alpha=0.8, linewidth=2)
        plt.plot(self.test_data.index, sarimax_test_pred, label='SARIMAX', alpha=0.8, linewidth=2)
        plt.plot(self.test_data.index, hw_test_pred, label='Holt-Winter', alpha=0.8, linewidth=2)
        plt.title('Final Test Set Predictions')
        plt.xlabel('Date')
        plt.ylabel('Demand')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.show()

        return sarimax_final_fitted, hw_final_fitted, sarimax_test_pred, hw_test_pred

    def step_ahead_forecasting(self, sarimax_model, hw_model):
        """Perform step-ahead forecasting at multiple horizons."""
        print("\n=== STEP-AHEAD FORECASTING ===")

        # Define forecasting horizons (adjusted for daily data)
        daily_horizons = [1, 3, 5, 7]  # t+1, t+3, t+5, t+7 (1-week ahead)
        weekly_horizons = [7, 14, 21, 30]  # t+7, t+14, t+21, t+30 (1-month ahead)

        # Prepare data for rolling forecasts
        train_val_data = pd.concat([self.train_data, self.val_data])
        exog_vars = ['Temperature', 'IsHoliday', 'WeekDay_sin', 'WeekDay_cos', 'DayOfYear_sin', 'DayOfYear_cos']

        # Perform rolling forecasts for each horizon
        print("Performing rolling forecasts...")

        # Daily horizons
        print("\n--- Daily Forecasting Horizons ---")
        for horizon in daily_horizons:
            self.rolling_forecast_horizon(train_val_data, exog_vars, horizon, f"Daily t+{horizon}")

        # Weekly horizons
        print("\n--- Weekly Forecasting Horizons ---")
        for horizon in weekly_horizons:
            self.rolling_forecast_horizon(train_val_data, exog_vars, horizon, f"Weekly t+{horizon}")

    def rolling_forecast_horizon(self, train_data, exog_vars, horizon, title):
        """Perform rolling forecast for a specific horizon."""
        print(f"\nForecasting horizon: {title}")

        # Limit to 50 forecasts for efficiency
        n_forecasts = min(50, len(self.test_data) - horizon)

        sarimax_predictions = []
        hw_predictions = []
        actual_values = []
        forecast_dates = []

        for i in range(0, n_forecasts, 7):  # Refit every 7 steps (weekly)
            # Current training data
            current_train_end = len(train_data) + i
            current_train_data = pd.concat([train_data, self.test_data.iloc[:i]]) if i > 0 else train_data

            # Fit models
            try:
                # SARIMAX
                sarimax_temp = SARIMAX(
                    current_train_data['Demand'],
                    exog=current_train_data[exog_vars],
                    order=(1, 1, 1),
                    seasonal_order=(1, 0, 1, 7),
                    enforce_stationarity=False,
                    enforce_invertibility=False
                )
                sarimax_fitted_temp = sarimax_temp.fit(disp=False)

                # Holt-Winter
                hw_temp = ExponentialSmoothing(
                    current_train_data['Demand'],
                    trend='add',
                    seasonal='mul',
                    seasonal_periods=7
                )
                hw_fitted_temp = hw_temp.fit(optimized=True)

                # Generate forecasts for next 7 steps
                for j in range(min(7, n_forecasts - i)):
                    forecast_idx = i + j
                    if forecast_idx + horizon >= len(self.test_data):
                        break

                    # Get exogenous data for forecast
                    forecast_exog = self.test_data.iloc[forecast_idx:forecast_idx+horizon+1][exog_vars]

                    # SARIMAX forecast
                    sarimax_pred = sarimax_fitted_temp.forecast(steps=horizon+1, exog=forecast_exog)[-1]

                    # Holt-Winter forecast
                    hw_pred = hw_fitted_temp.forecast(steps=horizon+1)[-1]

                    # Actual value
                    actual = self.test_data.iloc[forecast_idx + horizon]['Demand']

                    sarimax_predictions.append(sarimax_pred)
                    hw_predictions.append(hw_pred)
                    actual_values.append(actual)
                    forecast_dates.append(self.test_data.index[forecast_idx + horizon])

            except Exception as e:
                print(f"Error at step {i}: {e}")
                continue

        # Calculate MAPE
        if len(actual_values) > 0:
            sarimax_mape = mean_absolute_percentage_error(actual_values, sarimax_predictions) * 100
            hw_mape = mean_absolute_percentage_error(actual_values, hw_predictions) * 100

            print(f"SARIMAX MAPE: {sarimax_mape:.2f}%")
            print(f"Holt-Winter MAPE: {hw_mape:.2f}%")

            # Individual chart for this horizon
            plt.figure(figsize=(15, 7))
            plt.plot(forecast_dates, actual_values, label='Actual', alpha=0.8, linewidth=2, color='blue')
            plt.plot(forecast_dates, sarimax_predictions, label='SARIMAX', alpha=0.8, linewidth=2, color='red')
            plt.plot(forecast_dates, hw_predictions, label='Holt-Winter', alpha=0.8, linewidth=2, color='green')
            plt.title(f'Forecast performance at {title}')
            plt.xlabel('Date')
            plt.ylabel('Demand')
            plt.legend()
            plt.grid(True)
            plt.tight_layout()
            plt.show()
        else:
            print("No valid forecasts generated for this horizon")

    def run_complete_analysis(self):
        """Run the complete 3-phase forecasting analysis."""
        print("Starting Electric Demand Forecasting Analysis")
        print("=" * 60)

        # Phase 1: Data Preparation
        self.load_and_prepare_data()
        self.exploratory_data_analysis()
        self.create_train_val_test_split()

        # Phase 2: Initial Training
        sarimax_fitted, hw_fitted = self.phase2_initial_training()

        # Phase 3: Final Training and Testing
        sarimax_final, hw_final, sarimax_pred, hw_pred = self.phase3_final_training_and_testing()

        # Step-ahead forecasting
        self.step_ahead_forecasting(sarimax_final, hw_final)

        print("\n" + "=" * 60)
        print("Analysis completed successfully!")


def main():
    """Main execution function."""
    # Initialize forecaster
    forecaster = ElectricDemandForecaster('electric_demand_1h.csv')

    # Run complete analysis
    forecaster.run_complete_analysis()


if __name__ == "__main__":
    main()
